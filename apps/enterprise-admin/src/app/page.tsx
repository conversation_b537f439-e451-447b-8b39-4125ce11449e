'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Input,
} from '@b6ai/ui';

export default function Index() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto p-8">
        <div className="space-y-8">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              Welcome to Enterprise Admin 👋
            </h1>
            <p className="text-muted-foreground">
              Showcasing shadcn/ui components with centralized theming
            </p>
          </div>

          {/* Theme Toggle Section */}
          {/* <Card>
            <CardHeader>
              <CardTitle>Theme Demo</CardTitle>
              <CardDescription>
                Toggle between light and dark themes to see the centralized
                theming in action
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <Button
                  onClick={() =>
                    document.documentElement.classList.remove('dark')
                  }
                  variant="outline"
                >
                  Light Theme
                </Button>
                <Button
                  onClick={() => document.documentElement.classList.add('dark')}
                  variant="outline"
                >
                  Dark Theme
                </Button>
              </div>
            </CardContent>
          </Card> */}

          {/* Components Showcase */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* <Card>
              <CardHeader>
                <CardTitle>Buttons</CardTitle>
                <CardDescription>Various button variants</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full">Default</Button>
                <Button variant="secondary" className="w-full">
                  Secondary
                </Button>
                <Button variant="outline" className="w-full">
                  Outline
                </Button>
                <Button variant="ghost" className="w-full">
                  Ghost
                </Button>
                <Button variant="destructive" className="w-full">
                  Destructive
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Inputs</CardTitle>
                <CardDescription>Form input examples</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Input placeholder="Enter your name" />
                <Input type="email" placeholder="Enter your email" />
                <Input type="password" placeholder="Enter password" />
                <Input disabled placeholder="Disabled input" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Card Example</CardTitle>
                <CardDescription>This is a card component</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Cards are versatile containers that can hold various types of
                  content. They work seamlessly with the centralized theming
                  system.
                </p>
              </CardContent>
            </Card> */}
          </div>
        </div>
      </div>
    </div>
  );
}
