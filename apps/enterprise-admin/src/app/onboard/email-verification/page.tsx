'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Mail, CheckCircle, AlertCircle } from 'lucide-react';

export default function EmailVerificationPage() {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState('');
  const [resendTimer, setResendTimer] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const email = '<EMAIL>'; // This would come from props or context

  // Timer for resend functionality
  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => setResendTimer(resendTimer - 1), 1000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [resendTimer]);

  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return; // Only allow single digit

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError(''); // Clear error when user types

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').slice(0, 6);
    const newOtp = [...otp];

    for (let i = 0; i < pastedData.length && i < 6; i++) {
      if (/^\d$/.test(pastedData[i])) {
        newOtp[i] = pastedData[i];
      }
    }
    setOtp(newOtp);
  };

  const handleVerify = async () => {
    const otpCode = otp.join('');

    if (otpCode.length !== 6) {
      setError('Please enter all 6 digits');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // For demo purposes, accept any 6-digit code
      if (otpCode === '123456') {
        setIsVerified(true);
      } else {
        setError('Invalid verification code. Please try again.');
      }
    } catch (err) {
      setError('Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = async () => {
    setResendTimer(60); // 60 seconds cooldown
    setError('');

    try {
      // Simulate resend API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      // Show success message or handle resend logic
    } catch (err) {
      setError('Failed to resend code. Please try again.');
    }
  };

  if (isVerified) {
    return (
      <div className="min-h-screen bg-[#0a0f1c] flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <div className="bg-[#0a0f1c] rounded-2xl shadow-lg p-8 text-center border-2 border-white">
            <div className="w-16 h-16 bg-[#37B8E6] rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-white mb-4">
              Email Verified Successfully!
            </h1>
            <p className="text-gray-400 mb-8">
              Your email has been verified. You can now continue with your
              account setup.
            </p>
            <Button
              className="w-full h-12 bg-[#37B8E6] hover:bg-[#041C91] text-white font-medium rounded-lg"
              onClick={() => {
                // Navigate to next step
                window.location.href = '/onboard/company-details';
              }}
            >
              Continue Setup
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0a0f1c] flex items-center justify-center p-8">
      <div className="w-full max-w-md">
        <div className="bg-[#0a0f1c] rounded-2xl shadow-lg p-8 border-2 border-white">
          {/* Header */}
          <div className="text-center mb-8">
            <Button
              variant="ghost"
              className="absolute top-8 left-8 p-2 text-white"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>

            <div className="w-16 h-16 bg-[#37B8E6] rounded-full flex items-center justify-center mx-auto mb-6">
              <Mail className="w-8 h-8 text-white" />
            </div>

            <h1 className="text-2xl font-bold text-white mb-2">
              Verify Your Email
            </h1>
            <p className="text-gray-400">
              We've sent a 6-digit verification code to
            </p>
            <p className="text-white font-medium">{email}</p>
          </div>

          {/* OTP Input */}
          <div className="mb-6">
            <div className="flex gap-3 justify-center mb-4">
              {otp.map((digit, index) => (
                <Input
                  key={index}
                  ref={(el) => {
                    inputRefs.current[index] = el;
                  }}
                  type="text"
                  inputMode="numeric"
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleOtpChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  onPaste={handlePaste}
                  className={`w-12 h-12 text-center text-lg font-semibold border-2 rounded-lg transition-all text-white bg-[#0a0f1c] ${
                    error
                      ? 'border-red-400 focus:border-red-500'
                      : 'border-[#37B8E6] focus:border-[#041C91]'
                  }`}
                />
              ))}
            </div>

            {error && (
              <div className="flex items-center gap-2 text-red-400 text-sm">
                <AlertCircle className="w-4 h-4" />
                {error}
              </div>
            )}
          </div>

          {/* Verify Button */}
          <Button
            onClick={handleVerify}
            disabled={isLoading || otp.join('').length !== 6}
            className="w-full h-12 bg-[#37B8E6] hover:bg-[#041C91] disabled:bg-gray-700 text-white font-medium rounded-lg transition-colors mb-6"
          >
            {isLoading ? 'Verifying...' : 'Verify Email'}
          </Button>

          {/* Resend Code */}
          <div className="text-center">
            <p className="text-gray-400 text-sm mb-2">
              Didn't receive the code?
            </p>
            <Button
              variant="link"
              onClick={handleResend}
              disabled={resendTimer > 0}
              className="text-[#37B8E6] hover:text-[#041C91] p-0 h-auto font-medium"
            >
              {resendTimer > 0
                ? `Resend in ${resendTimer}s`
                : 'Resend verification code'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
