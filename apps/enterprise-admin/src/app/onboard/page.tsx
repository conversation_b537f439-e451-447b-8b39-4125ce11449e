'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from '@/components/ui/card';
import { Mail, CheckCircle } from 'lucide-react';

export default function SignupPage() {
  const [email, setEmail] = useState('');

  return (
    <div className="min-h-screen bg-background text-foreground flex items-center justify-center">
      <div className="container mx-auto p-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          {/* Left section */}
          <div className="space-y-6">
            <h1 className="text-4xl font-bold">
              Connect <br /> with <span className="text-primary">b6ai</span>
            </h1>

            {/* Work Email Input */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Work email</label>
              <div className="flex items-center border rounded-md px-3 py-2 bg-background">
                <Mail className="text-muted-foreground w-5 h-5 mr-2" />
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="border-0 focus-visible:ring-0 p-0 flex-1 bg-transparent"
                />
                {email && <CheckCircle className="text-green-500 w-5 h-5" />}
              </div>
            </div>

            {/* Primary Action */}
            <Button className="w-full">Sign up</Button>

            {/* Social Buttons */}
            <div className="flex gap-4">
              <Button variant="outline" className="flex-1">
                Google
              </Button>
              <Button variant="outline" className="flex-1">
                Microsoft
              </Button>
            </div>

            {/* Footer link */}
            <p className="text-sm text-muted-foreground">
              Trying to access b6ai?{' '}
              <Button variant="link" asChild>
                <a href="#">Log in</a>
              </Button>
            </p>
          </div>

          {/* Right section */}
          <div className="flex justify-center">
            <Card className="w-full max-w-sm">
              <CardHeader>
                <CardTitle>Team workload</CardTitle>
                <CardDescription>
                  Visualize and manage your team’s tasks seamlessly
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="w-full h-32 rounded-md bg-muted flex items-center justify-center text-muted-foreground">
                  📊 Placeholder for chart/illustration
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
