.circle-wrapper {
  filter: blur(10px);
  align-content: center;
  align-items: center;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  height: min-content;
  justify-content: center;
  left: 50%;
  opacity: 0.6;
  /* overflow: hidden; */
  padding: 0;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: min-content;
  z-index: 1;
  clip-path: circle(203px at center);
}

.big-circle {
  aspect-ratio: 1/1;
  background: linear-gradient(
    229deg,
    #df7afe 13%,
    rgba(201, 110, 240, 0) 35.0235827429153%,
    rgba(164, 92, 219, 0) 64.17244225559735%,
    rgb(129, 74, 200) 88%
  );
  border-radius: 363px;
  flex: none;
  gap: 10px;
  height: 406px;
  /* overflow: hidden; */
  position: relative;
  width: 406px;
  will-change: transform;
}

.small-circle {
  aspect-ratio: 1/1;
  background: linear-gradient(
    229deg,
    #df7afe 13%,
    rgba(201, 110, 240, 0) 35.0235827429153%,
    rgba(164, 92, 219, 0) 64.17244225559735%,
    rgb(129, 74, 200) 88%
  );
  border-radius: 363px;
  flex: none;
  height: 300px;
  left: 50%;
  overflow: hidden;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  will-change: transform;
  z-index: 1;
}
.big-circle,
.small-circle {
  box-sizing: border-box;
}

.big-circle {
  outline: 1px solid red;
}
