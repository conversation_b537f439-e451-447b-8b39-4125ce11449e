import { Metadata } from 'next';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import AboutHero from '@/components/sections/AboutHero';
import AboutContent from '@/components/sections/AboutContent';
import IndustriesServed from '@/components/sections/IndustriesServed';
import { getAboutPageData } from '@/lib/api';

export async function generateMetadata(): Promise<Metadata> {
  try {
    const data = await getAboutPageData();

    return {
      title: data.seo.title,
      description: data.seo.description,
      keywords: data.seo.keywords,
      openGraph: {
        title: data.seo.title,
        description: data.seo.description,
        url: data.seo.ogUrl,
        images: data.seo.ogImage ? [{ url: data.seo.ogImage }] : undefined,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'About Us - BytesNBinary',
      description: 'Learn about our story, mission, and team.',
    };
  }
}

export default async function AboutPage() {
  try {
    const data = await getAboutPageData();

    return (
      <>
        <Header />
        <AboutHero data={data} />
        <AboutContent data={data} />
        <IndustriesServed />
        <Footer />
      </>
    );
  } catch (error) {
    console.error('Error loading about page data:', error);
    return (
      <>
        <Header />
        <div className="container section-padding text-center">
          <h1 className="text-4xl font-bold mb-4">About Us</h1>
          <p className="text-lg text-muted-foreground">
            We are experiencing technical difficulties. Please try again later.
          </p>
        </div>
        <Footer />
      </>
    );
  }
}
