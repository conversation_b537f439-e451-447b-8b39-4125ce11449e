/* 
@import "tailwindcss";


:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
} */


@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');

@layer base {
  body {
    @apply bg-black text-white font-sans;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading text-white;
  }
}

@layer components {
  .container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-16 lg:py-24;
  }

  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply btn bg-primary text-white hover:bg-primary-600 h-10 py-2 px-4;
  }

  .btn-secondary {
    @apply btn bg-secondary text-white hover:bg-secondary-600 h-10 py-2 px-4;
  }

  .btn-outline {
    @apply btn border border-gray-300 hover:bg-gray-50 hover:text-gray-900 h-10 py-2 px-4;
  }

  .btn-ghost {
    @apply btn hover:bg-gray-100 hover:text-gray-900 h-10 py-2 px-4;
  }

  .btn-link {
    @apply btn underline-offset-4 hover:underline text-primary h-10 py-2 px-4;
  }

  .btn-lg {
    @apply h-11 px-8 rounded-md;
  }

  .btn-sm {
    @apply h-9 px-3 rounded-md;
  }

  .card {
    @apply rounded-lg border border-gray-700 bg-gray-900 text-white shadow-lg backdrop-blur-sm;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-gray-600 bg-gray-800 text-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .textarea {
    @apply flex min-h-[80px] w-full rounded-md border border-gray-600 bg-gray-800 text-white px-3 py-2 text-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .galaxy-bg {
    background: linear-gradient(45deg, #000000, #000000, #000000, #000000);
    background-size: 400% 400%;
    animation: galaxyMove 20s ease infinite;
    position: relative;
    overflow: hidden;
  }

  .galaxy-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(2px 2px at 20px 30px, #eee, transparent),
      radial-gradient(
        2px 2px at 40px 70px,
        rgba(255, 255, 255, 0.8),
        transparent
      ),
      radial-gradient(1px 1px at 90px 40px, #fff, transparent),
      radial-gradient(
        1px 1px at 130px 80px,
        rgba(255, 255, 255, 0.6),
        transparent
      ),
      radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: stars 50s linear infinite;
  }

  .galaxy-bg::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(
        1px 1px at 50px 50px,
        rgba(255, 255, 255, 0.4),
        transparent
      ),
      radial-gradient(
        1px 1px at 100px 100px,
        rgba(255, 255, 255, 0.3),
        transparent
      ),
      radial-gradient(
        1px 1px at 150px 150px,
        rgba(255, 255, 255, 0.5),
        transparent
      );
    background-repeat: repeat;
    background-size: 300px 300px;
    animation: stars 80s linear infinite reverse;
  }

  .floating-element {
    animation: float 6s ease-in-out infinite;
  }

  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite alternate;
  }

  .text-glow {
    text-shadow:
      0 0 10px rgba(59, 130, 246, 0.5),
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 30px rgba(59, 130, 246, 0.2);
  }

  .card-hover {
    transition: all 0.3s ease;
  }

  .card-hover:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
  }
}

@keyframes galaxyMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes stars {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-200px);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

@keyframes pulseGlow {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  100% {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.8),
      0 0 30px rgba(59, 130, 246, 0.6);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
}

/* Selection styles */
::selection {
  background-color: hsl(var(--primary) / 0.2);
  color: hsl(var(--primary-foreground));
}
