// import type { Metadata } from "next";
// import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
// import "./globals.css";

// const geistSans = Geist({
//   variable: "--font-geist-sans",
//   subsets: ["latin"],
// });

// const geistMono = Geist_Mono({
//   variable: "--font-geist-mono",
//   subsets: ["latin"],
// });

// export const metadata: Metadata = {
//   title: "Create Next App",
//   description: "Generated by create next app",
// };

// export default function RootLayout({
//   children,
// }: Readonly<{
//   children: React.ReactNode;
// }>) {
//   return (
//     <html lang="en">
//       <body
//         className={`${geistSans.variable} ${geistMono.variable} antialiased`}
//       >
//         {children}
//       </body>
//     </html>
//   );
// }
// import './styles/globals.css';
import './globals.css';
import type { Metadata } from 'next';
import { Inter, Poppins } from 'next/font/google';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-poppins',
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: 'BytesNBinary - Digital Solutions & Technology Services',
    template: '%s | BytesNBinary',
  },
  description:
    'Leading provider of digital solutions, web development, and technology services. Transform your business with our innovative solutions.',
  keywords: [
    'digital solutions',
    'web development',
    'technology services',
    'software development',
    'digital transformation',
  ],
  authors: [{ name: 'BytesNBinary Team' }],
  creator: 'BytesNBinary',
  publisher: 'BytesNBinary',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  ),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    siteName: 'BytesNBinary',
    title: 'BytesNBinary - Digital Solutions & Technology Services',
    description:
      'Leading provider of digital solutions, web development, and technology services. Transform your business with our innovative solutions.',
    images: [
      {
        url: '/images/og/og-default.jpg',
        width: 1200,
        height: 630,
        alt: 'BytesNBinary - Digital Solutions',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@bytesnbinary',
    creator: '@bytesnbinary',
    title: 'Bytes N Binary - Digital Solutions & Technology Services',
    description:
      'Leading provider of digital solutions, web development, and technology services. Transform your business with our innovative solutions.',
    images: ['/images/og/og-default.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
  },
  icons: {
    icon: '/images/logo_icon.png', // PNG favicon
    shortcut: '/images/logo_icon.png',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <body className="min-h-screen bg-black font-sans antialiased">
        <div className="relative flex min-h-screen flex-col">
          <main className="flex-1">{children}</main>
        </div>
      </body>
    </html>
  );
}
