import { Metadata } from 'next';
import { getServicesData } from '@/lib/api';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import ServicesGrid from '@/components/sections/ServicesGrid';
import { AnimatedText } from '@/components/ui/AnimatedText';

export const metadata: Metadata = {
  title: 'Our Services - BytesNBinary',
  description:
    'Explore our comprehensive range of digital services including web development, mobile apps, cloud solutions, and AI integration.',
};

export default async function ServicesPage() {
  try {
    const data = await getServicesData();

    return (
      <>
        <Header />
        <section className="section-padding">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center mb-16">
              <AnimatedText
                text={data.title}
                as="h1"
                type="letter"
                className="text-4xl md:text-5xl font-heading font-bold mb-6"
              />

              <AnimatedText
                text={data.description}
                as="p"
                type="word"
                className="text-lg text-muted-foreground max-w-3xl mx-auto"
              />
            </div>
            <ServicesGrid services={data.services} />
          </div>
        </section>
        <Footer />
      </>
    );
  } catch (error) {
    console.error('Error loading services page data:', error);
    return (
      <>
        <Header />
        <div className="container section-padding text-center">
          <h1 className="text-4xl font-bold mb-4">Our Services</h1>
          <p className="text-lg text-muted-foreground">
            We are experiencing technical difficulties. Please try again later.
          </p>
        </div>
        <Footer />
      </>
    );
  }
}
