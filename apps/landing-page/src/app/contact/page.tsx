import { Metadata } from 'next';

import ContactForm from '@/components/sections/ContactForm';
import ContactInfo from '@/components/sections/ContactInfo';
import { AnimatedText } from '@/components/ui/AnimatedText';
import { getContactInfo } from '@/lib/api';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

export const metadata: Metadata = {
  title: 'Contact Us - BytesNBinary',
  description:
    "Get in touch with BytesNBinary for your next project. We're here to help transform your business with digital solutions.",
};

export default async function ContactPage() {
  try {
    const contactData = await getContactInfo();
    return (
      <>
        <Header />
        <section className="section-padding">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center mb-16">
              <AnimatedText
                text={'Get In Touch'}
                as="h1"
                type="letter"
                className="text-4xl md:text-5xl font-heading font-bold mb-6"
              />

              <AnimatedText
                text={`Ready to start your project? Contact us today for a free consultation and discover how we can help transform your business.`}
                as="p"
                type="word"
                className="text-lg text-muted-foreground max-w-3xl mx-auto"
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
              <ContactForm />
              <ContactInfo data={contactData} />
            </div>
          </div>
        </section>
        <Footer />
      </>
    );
  } catch (error) {
    console.error('Error loading contact page data:', error);
    return (
      <>
        <Header />
        <div className="container section-padding text-center">
          <h1 className="text-4xl font-bold mb-4">Contact Us</h1>
          <p className="text-lg text-muted-foreground">
            We are experiencing technical difficulties. Please try again later.
          </p>
        </div>
        <Footer />
      </>
    );
  }
}
