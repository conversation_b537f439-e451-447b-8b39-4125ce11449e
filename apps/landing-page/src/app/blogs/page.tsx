import { Metadata } from 'next';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import BlogGrid from '@/components/sections/BlogGrid';
import { AnimatedText } from '@/components/ui/AnimatedText';
import { getBlogsData } from '@/lib/api';

export const metadata: Metadata = {
  title: 'Blog - BytesNBinary',
  description:
    'Stay updated with the latest technology trends, development insights, and industry news from the BytesNBinary team.',
};

export default async function BlogsPage() {
  try {
    const data = await getBlogsData();

    return (
      <>
        <Header />
        <section className="section-padding">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center mb-16">
              <AnimatedText
                text={data.title}
                as="h1"
                type="letter"
                className="text-4xl md:text-5xl font-heading font-bold mb-6"
              />

              <AnimatedText
                text={data.description}
                as="p"
                type="word"
                className="text-lg text-muted-foreground max-w-3xl mx-auto"
              />
            </div>
            <BlogGrid posts={data.posts} />
          </div>
        </section>
        <Footer />
      </>
    );
  } catch (error) {
    return (
      <>
        <Header />
        <div className="container section-padding text-center">
          <h1 className="text-4xl font-bold mb-4">Our Blog</h1>
          <p className="text-lg text-muted-foreground">
            We are experiencing technical difficulties. Please try again later.
          </p>
        </div>
        <Footer />
      </>
    );
  }
}
