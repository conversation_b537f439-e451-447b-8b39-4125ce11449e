'use client';

// import type { AboutPageData } from '@bytesnbinary/shared/types';
import type { AboutPageData } from '../../../shared/types';
import { motion } from 'framer-motion';
import { AnimatedText } from '../ui/AnimatedText';

interface AboutHeroProps {
  data: AboutPageData;
}

export default function AboutHero({ data }: AboutHeroProps) {
  return (
    <section className="section-padding bg-black relative overflow-hidden">
      <div className="container">
        <div className="max-w-4xl mx-auto text-center mb-16">
          <AnimatedText
            text={data.title}
            as="h1"
            type="letter"
            className="text-4xl md:text-5xl font-heading font-bold mb-6"
          />

          <AnimatedText
            text={data.description}
            as="p"
            type="word"
            className="text-lg md:text-xl text-gray-300 text-balance"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="card p-8 text-center"
          >
            <div className="text-4xl mb-4">🎯</div>
            <h3 className="text-xl font-semibold mb-4">Our Mission</h3>
            <p className="text-gray-300">{data.mission}</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="card p-8 text-center"
          >
            <div className="text-4xl mb-4">🔮</div>
            <h3 className="text-xl font-semibold mb-4">Our Vision</h3>
            <p className="text-gray-300">{data.vision}</p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="card p-8 text-center"
          >
            <div className="text-4xl mb-4">✨</div>
            <h3 className="text-xl font-semibold mb-4">Our Story</h3>
            <p className="text-gray-300">{data.story}</p>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
