'use client';

import { motion } from 'framer-motion';
import type { ContactInfo as ContactInfoType } from '../../../shared/types';
// import type { ContactInfo as ContactInfoType } from '@bytesnbinary/shared/types';

interface ContactInfoProps {
  data: ContactInfoType;
}

export default function ContactInfo({ data }: ContactInfoProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-8"
    >
      <div>
        <h2 className="text-2xl font-semibold mb-6">Contact Information</h2>
        <div className="space-y-6">
          <div className="flex items-start space-x-4">
            <div className="text-2xl">📧</div>
            <div>
              <h3 className="font-medium mb-1">Email</h3>
              <a
                href={`mailto:${data.email}`}
                className="text-primary hover:underline"
              >
                {data.email}
              </a>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="text-2xl">📞</div>
            <div>
              <h3 className="font-medium mb-1">Phone</h3>
              <a
                href={`tel:${data.phone}`}
                className="text-primary hover:underline"
              >
                {data.phone}
              </a>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="text-2xl">📍</div>
            <div>
              <h3 className="font-medium mb-1">Address</h3>
              <div className="text-muted-foreground">
                <p>{data.address.building}</p>
                <p>{data.address.street}</p>
                <p>
                  {data.address.city}, {data.address.state}{' '}
                  {data.address.zipCode}
                </p>
                <p>{data.address.country}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h3 className="font-medium mb-4">Follow Us</h3>
        <div className="flex space-x-4">
          {data.socialMedia.facebook && (
            <a
              href={data.socialMedia.facebook}
              target="_blank"
              rel="noopener noreferrer"
              className="text-2xl hover:text-primary transition-colors"
            >
              📘
            </a>
          )}
          {data.socialMedia.twitter && (
            <a
              href={data.socialMedia.twitter}
              target="_blank"
              rel="noopener noreferrer"
              className="text-2xl hover:text-primary transition-colors"
            >
              𝕏
            </a>
          )}
          {data.socialMedia.linkedin && (
            <a
              href={data.socialMedia.linkedin}
              target="_blank"
              rel="noopener noreferrer"
              className="text-2xl hover:text-primary transition-colors"
            >
              💼
            </a>
          )}
          {data.socialMedia.instagram && (
            <a
              href={data.socialMedia.instagram}
              target="_blank"
              rel="noopener noreferrer"
              className="text-2xl hover:text-primary transition-colors"
            >
              📷
            </a>
          )}
        </div>
      </div>

      <div className="card p-6 bg-primary/5">
        <h3 className="font-medium mb-2">Business Hours</h3>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
          <p>Saturday: 10:00 AM - 4:00 PM</p>
          <p>Sunday: Closed</p>
        </div>
      </div>
    </motion.div>
  );
}
