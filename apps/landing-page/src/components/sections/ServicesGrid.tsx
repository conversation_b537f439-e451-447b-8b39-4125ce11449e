'use client';

import { motion } from 'framer-motion';
import type { Service } from '../../../shared/types';
// import type { Service } from '@bytesnbinary/shared/types';

interface ServicesGridProps {
  services: Service[];
}

export default function ServicesGrid({ services }: ServicesGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {services.map((service, index) => (
        <motion.div
          key={service.id}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: index * 0.1 }}
          viewport={{ once: true }}
          className="card p-6 hover:shadow-lg transition-all duration-300 group"
        >
          <div className="mb-4">
            <span className="inline-block px-3 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full mb-4">
              {service.category}
            </span>
            <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors">
              {service.title}
            </h3>
            <p className="text-muted-foreground mb-4">{service.description}</p>
          </div>

          <div className="mb-6">
            <h4 className="font-medium mb-3">HighLights:</h4>
            <ul className="space-y-1">
              {service.features.map((feature, idx) => (
                <li
                  key={idx}
                  className="text-sm text-muted-foreground flex items-center"
                >
                  <span className="text-primary mr-2">✓</span>
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        </motion.div>
      ))}
    </div>
  );
}
