'use client';

import type { AboutPageData } from '../../../shared/types';
import { motion } from 'framer-motion';

interface AboutSectionProps {
  data: AboutPageData;
}

export default function AboutContent({ data }: AboutSectionProps) {
  return (
    <section className="section-padding bg-black relative">
      <div className="container relative z-10">
        {/* Heading */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-heading font-bold mb-4 text-white">
            {data?.values?.title}
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            {data?.values?.description}
          </p>
        </motion.div>

        {/* Glow Background */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-purple-700/20 blur-3xl rounded-full pointer-events-none" />

        {/* Values Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 relative">
          {data?.values?.points?.map((point, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-[#0d0d0dcc] rounded-2xl p-6 flex flex-col relative z-10 
                     border border-purple-500/20 
                     shadow-[0_0_15px_rgba(168,85,247,0.15)] 
                     hover:shadow-[0_0_25px_rgba(168,85,247,0.4)] 
                     transition-shadow duration-500 ease-in-out"
            >
              {/* Icon + Title */}
              <div className="flex items-center mb-3">
                <span className="text-2xl text-purple-400 mr-3">
                  {point?.icon}
                </span>
                <h3 className="text-xl font-semibold text-white">
                  {point?.title}
                </h3>
              </div>
              {/* Subtitle */}
              <p className="text-gray-400 text-sm">{point?.subtitle}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
