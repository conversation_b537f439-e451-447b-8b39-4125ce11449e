'use client';

// import type { TeamMember } from '@bytesnbinary/shared/types';
import type { TeamMember } from '../../../shared/types';
import { motion } from 'framer-motion';

interface TeamSectionProps {
  data: TeamMember[];
}

export default function TeamSection({ data }: TeamSectionProps) {
  return (
    <section className="section-padding bg-black">
      <div className="container">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-heading font-bold mb-4">
            Meet Our Team
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Our passionate team of experts is dedicated to delivering
            exceptional results for every project.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {data.map((member, index) => (
            <motion.div
              key={member.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="card p-6 text-center group hover:shadow-lg transition-all duration-300 card-hover"
            >
              <div className="w-24 h-24 bg-gradient-to-br from-primary to-accent-600 rounded-full mx-auto mb-4 flex items-center justify-center text-white text-2xl font-bold">
                {member.name
                  .split(' ')
                  .map((n) => n[0])
                  .join('')}
              </div>
              <h3 className="text-lg font-semibold mb-1">{member.name}</h3>
              <p className="text-blue-400 text-sm font-medium mb-3">
                {member.position}
              </p>
              <p className="text-gray-300 text-sm mb-4">{member.bio}</p>

              {member.social && (
                <div className="flex justify-center space-x-3">
                  {member.social.linkedin && (
                    <a
                      href={member.social.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-blue-400 transition-colors"
                    >
                      💼
                    </a>
                  )}
                  {member.social.twitter && (
                    <a
                      href={member.social.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors"
                    >
                      𝕏
                    </a>
                  )}
                  {member.social.github && (
                    <a
                      href={member.social.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors"
                    >
                      🐙
                    </a>
                  )}
                </div>
              )}
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
