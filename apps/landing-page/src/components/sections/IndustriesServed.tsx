'use client';
import { motion } from 'framer-motion';

const industries = [
  { icon: '🏥', name: 'Healthcare' },
  { icon: '🏦', name: 'Finance' },
  { icon: '🛒', name: 'E-commerce' },
  { icon: '✈️', name: 'Travel' },
  { icon: '🎓', name: 'Education' },
  { icon: '🏗️', name: 'Construction' },
  { icon: '⚡', name: 'Energy' },
  { icon: '📱', name: 'Technology' },
];

export default function IndustriesServed() {
  return (
    <section className="section-padding bg-gradient-to-b from-black to-gray-900 relative overflow-hidden">
      <div className="container">
        {/* Heading */}
        <motion.h2
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
          viewport={{ once: true }}
          className="text-3xl md:text-4xl font-heading font-bold text-center text-white mb-4"
        >
          Industries We Serve
        </motion.h2>

        {/* Optional Subheading */}
        <motion.p
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2, ease: 'easeOut' }}
          viewport={{ once: true }}
          className="text-center text-gray-300 max-w-2xl mx-auto mb-12"
        >
          We partner with diverse industries to deliver tailored solutions that
          drive growth, efficiency, and innovation.
        </motion.p>

        {/* Grid */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={{
            hidden: {},
            visible: { transition: { staggerChildren: 0.15 } },
          }}
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-8 max-w-5xl mx-auto"
        >
          {industries.map((industry, i) => (
            <motion.div
              variants={{
                hidden: { opacity: 0, y: 40, scale: 0.95 },
                visible: {
                  opacity: 1,
                  y: 0,
                  scale: 1.05, // permanent slight scale
                  transition: { duration: 0.5, ease: 'easeOut' },
                },
              }}
              whileHover={{ scale: 1.12 }} // bulge effect on hover
              transition={{ type: 'spring', stiffness: 300, damping: 15 }}
              className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 text-center shadow-lg shadow-indigo-500/40"
            >
              <div className="text-4xl mb-3">{industry.icon}</div>
              <h3 className="text-lg font-semibold text-white">
                {industry.name}
              </h3>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
