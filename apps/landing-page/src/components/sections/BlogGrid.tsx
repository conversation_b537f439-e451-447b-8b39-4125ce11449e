'use client';

import { motion } from 'framer-motion';
import type { BlogPost } from '../../../shared/types';
// import type { BlogPost } from '@bytesnbinary/shared/types';

interface BlogGridProps {
  posts: BlogPost[];
}

export default function BlogGrid({ posts }: BlogGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {posts.map((post, index) => (
        <motion.article
          key={post.id}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: index * 0.1 }}
          viewport={{ once: true }}
          className="card overflow-hidden hover:shadow-lg transition-all duration-300 group"
        >
          <div className="aspect-video bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
            <span className="text-4xl">📝</span>
          </div>

          <div className="p-6">
            <div className="flex items-center justify-between mb-3">
              <span className="inline-block px-3 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
                {post.category}
              </span>
              <time className="text-xs text-muted-foreground">
                {new Date(post.publishedAt).toLocaleDateString()}
              </time>
            </div>

            <h3 className="text-lg font-semibold mb-3 group-hover:text-primary transition-colors line-clamp-2">
              {post.title}
            </h3>

            <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
              {post.excerpt}
            </p>

            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                By {post.author}
              </span>
              <button className="text-sm font-medium text-primary hover:text-primary/80 transition-colors">
                Read More →
              </button>
            </div>

            <div className="flex flex-wrap gap-1 mt-4">
              {post.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag}
                  className="text-xs px-2 py-1 bg-secondary/50 text-secondary-foreground rounded"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </motion.article>
      ))}
    </div>
  );
}
