'use client';

// import type {
//   HeroSection as HeroSectionType,
//   Statistics,
// } from '@bytesnbinary/shared/types';
import type {
  HeroSection as HeroSectionType,
  Statistics,
} from '../../../shared/types';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { AnimatedCircle } from '../ui/AnimatedCircle';

interface HeroSectionProps {
  data: HeroSectionType;
  statistics: Statistics[];
}

export default function HeroSection({ data, statistics }: HeroSectionProps) {
  return (
    <section className="relative min-h-[100vh] flex items-center justify-center overflow-hidden galaxy-bg">
      {/* Floating particles */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-blue-400 rounded-full floating-element opacity-60"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 6}s`,
              animationDuration: `${4 + Math.random() * 4}s`,
            }}
          />
        ))}
      </div>

      <div className="container relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="relative"
          >
            {/* Animated circle behind the title */}
            <div className="absolute left-1/2 top-1/2  z-0">
              <AnimatedCircle />
            </div>

            <h1 className="text-4xl md:text-6xl lg:text-8xl font-heading font-bold mb-6 text-balance text-white relative z-10">
              {data.title}{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 text-glow">
                {data.subtitle}
              </span>
            </h1>
          </motion.div>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-lg md:text-xl text-gray-300 mb-8 max-w-2xl mx-auto text-balance"
          >
            {data.description}
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Link href={data.ctaLink} className="btn-primary btn-lg pulse-glow">
              {data.ctaText}
            </Link>
            <Link
              href="/about"
              className="btn-outline btn-lg border-white text-white hover:bg-white hover:text-black"
            >
              Learn More
            </Link>
          </motion.div>

          {/* Stats or additional info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto"
          >
            {statistics?.map((stat) => (
              <div key={stat.id} className="text-center">
                <div
                  className={`text-3xl font-bold ${stat.color} mb-2 text-glow`}
                >
                  {stat?.value}
                </div>
                <div className="text-sm text-gray-300">{stat?.title}</div>
              </div>
            ))}
          </motion.div>
        </div>
      </div>

      {/* Floating Elements */}
      <motion.div
        animate={{
          y: [0, -20, 0],
          rotate: [0, 5, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        className="absolute top-20 left-10 w-20 h-20 bg-primary/10 rounded-full blur-xl"
      />
      <motion.div
        animate={{
          y: [0, 20, 0],
          rotate: [0, -5, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        className="absolute bottom-20 right-10 w-32 h-32 bg-accent/10 rounded-full blur-xl"
      />
    </section>
  );
}
