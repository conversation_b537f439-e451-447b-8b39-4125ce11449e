'use client';
// import { FeatureItem, WhyUs } from '@bytesnbinary/shared/types';
import { motion } from 'framer-motion';
import { FC } from 'react';
import type { WhyUs } from '../../../shared/types';
// const points = [
//   'Modern & Responsive Design',
//   'Lightning Fast Performance',
//   'SEO Optimized',
//   'Cross-platform Compatible',
//   '24/7 Support & Maintenance',
// ];

interface FeaturesSectionProps {
  data: WhyUs;
}
const WhyUsSection: FC<FeaturesSectionProps> = ({ data }) => {
  return (
    <section className="section-padding bg-black relative overflow-hidden">
      <div className="container relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-heading font-bold mb-4 text-white text-glow">
              {data?.title}
            </h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              {data?.description}
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* LEFT: Auto-scroll box */}
            <div className="bg-[#0d0d0dcc] p-4 rounded-2xl overflow-hidden relative h-[250px]">
              <motion.div
                className="absolute top-0 left-0 w-full"
                animate={{ y: ['0%', '-50%'] }}
                transition={{
                  duration: 15,
                  ease: 'linear',
                  repeat: Infinity,
                }}
              >
                {[...data?.points, ...data?.points].map((point, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between h-auto min-h-[50px] px-4 border-b border-gray-700/30 py-2"
                  >
                    <div className="flex-1 min-w-0">
                      <p className="text-white font-medium break-words">
                        {point?.title}
                      </p>
                      <p className="text-gray-400 text-sm break-words">
                        {point?.subtitle}
                      </p>
                    </div>
                    <div className="text-purple-400 flex-shrink-0 ml-2">✔</div>
                  </div>
                ))}
              </motion.div>

              {/* Fade overlays */}
              <div className="absolute top-0 left-0 w-full h-10 bg-gradient-to-b from-[#0d0d0dcc] to-transparent pointer-events-none" />
              <div className="absolute bottom-0 left-0 w-full h-10 bg-gradient-to-t from-[#0d0d0dcc] to-transparent pointer-events-none" />
            </div>

            {/* RIGHT: Text content */}
            <div>
              <span className="inline-block bg-[#222] text-white px-3 py-1 rounded-md mb-4">
                {data?.highLights?.buttonLabel}
              </span>
              <h2 className="text-3xl font-bold text-white mb-4">
                {data?.highLights?.title}
              </h2>
              <p className="text-gray-400 mb-6">
                {data?.highLights?.description}
              </p>
              <div className="flex space-x-4">
                {data?.highLights?.footer.map((item, index) => (
                  <button
                    key={index}
                    className="bg-[#222] text-white px-4 py-2 rounded-lg"
                  >
                    {item}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyUsSection;
