import React from 'react';
import { motion } from 'framer-motion';
import '@/styles/animated-circle.css';

export const AnimatedCircle = () => {
  return (
    <div className={'circle-wrapper'}>
      <motion.div
        className={`relative big-circle`}
        animate={{ rotate: -360 }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: 'linear',
        }}
      />
      <motion.div
        className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1/2 h-1/2 rounded-full  small-circle`}
        animate={{ rotate: 360 }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: 'linear',
        }}
      />
    </div>
  );
};
