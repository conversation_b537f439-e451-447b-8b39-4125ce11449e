'use client';

import { motion } from 'framer-motion';

type AnimatedTextProps = {
  text: string;
  as?: 'h1' | 'p';
  type?: 'letter' | 'word'; // choose animation style
  className?: string;
};

export const AnimatedText = ({
  text,
  as: Tag = 'h1',
  type = 'letter',
  className = '',
}: AnimatedTextProps) => {
  const items = type === 'letter' ? text.split('') : text.split(' ');

  const container = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.05 },
    },
  };

  const child = {
    hidden: { opacity: 0, x: -20, filter: 'blur(8px)' },
    visible: {
      opacity: 1,
      x: 0,
      filter: 'blur(0px)',
      transition: { duration: 0.5, ease: 'easeOut' },
    },
  };

  return (
    <motion.div
      variants={container}
      initial="hidden"
      animate="visible"
      className="text-center"
    >
      <Tag className={className}>
        {items.map((item, i) => (
          <motion.span key={i} variants={child} className="inline-block">
            {item === ' ' ? '\u00A0' : item}
            {type === 'word' && i < items.length - 1 ? '\u00A0' : ''}
          </motion.span>
        ))}
      </Tag>
    </motion.div>
  );
};
