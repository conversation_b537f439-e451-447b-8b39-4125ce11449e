'use client';

import type { BannerData } from '@bytesnbinary/shared/types';
import { AnimatePresence, motion } from 'framer-motion';
import Link from 'next/link';
import { useState } from 'react';

interface BannerProps {
  data: BannerData;
}

export default function Banner({ data }: BannerProps) {
  const [isVisible, setIsVisible] = useState(true);

  if (!data.isActive || !isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ height: 0, opacity: 0 }}
        animate={{ height: 'auto', opacity: 1 }}
        exit={{ height: 0, opacity: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white relative overflow-hidden"
      >
        <div className="container py-3">
          <div className="flex flex-col items-center justify-center text-center relative">
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-sm font-medium">{data.text}</span>
              {data.link && (
                <Link
                  href={data.link}
                  className="text-sm font-semibold underline hover:no-underline transition-all"
                >
                  Learn More →
                </Link>
              )}
            </div>

            <button
              onClick={() => setIsVisible(false)}
              className="absolute right-0 p-1 hover:bg-white/10 rounded transition-colors"
              aria-label="Close banner"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
