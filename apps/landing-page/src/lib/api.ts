import {
  ApiResponse,
  HomePageData,
  AboutPageData,
  ContactInfo,
  ServicesPageData,
  BlogsPageData,
  BlogPost,
  Service,
} from '../../shared/types';

// Use the load balancer URL directly for production
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL?.replace('/api/v1', '') ||
  process.env.BACKEND_URL?.replace('/api/v1', '');
const API_PREFIX = '/api/v1';

class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function fetchApi<T>(endpoint: string): Promise<T> {
  const url = `${API_BASE_URL}${API_PREFIX}${endpoint}`;
  console.log('BACK-END URL =======', url);

  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
      },
      // Enable caching for SSR
      next: { revalidate: 3600 }, // Revalidate every hour
    });

    if (!response.ok) {
      throw new ApiError(
        response.status,
        `HTTP error! status: ${response.status}`
      );
    }

    const data: ApiResponse<T> = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'API request failed');
    }

    return data.data as T;
  } catch (error) {
    console.error(`API Error for ${endpoint}:`, error);

    // During build time, if API is not available, return fallback data
    if (
      process.env.NODE_ENV === 'production' &&
      error instanceof Error &&
      error.message.includes('ECONNREFUSED')
    ) {
      console.warn(
        `API not available during build for ${endpoint}, using fallback data`
      );
      // Return empty/fallback data structure
      return {} as T;
    }

    throw error;
  }
}

// Homepage API
export async function getHomePageData(): Promise<HomePageData> {
  return fetchApi<HomePageData>('/home');
}

export async function getHeroData() {
  return fetchApi('/home/<USER>');
}

export async function getFeaturesData() {
  return fetchApi('/home/<USER>');
}

// About page API
export async function getAboutPageData(): Promise<AboutPageData> {
  return fetchApi<AboutPageData>('/about');
}

export async function getTeamData() {
  return fetchApi('/about/team');
}

export async function getTeamMember(id: string) {
  return fetchApi(`/about/team/${id}`);
}

// Contact API
export async function getContactInfo(): Promise<ContactInfo> {
  return fetchApi<ContactInfo>('/contact');
}

export async function submitContactForm(data: {
  name: string;
  email: string;
  subject: string;
  message: string;
}) {
  const url = `${API_BASE_URL}${API_PREFIX}/contact/message`;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new ApiError(
      response.status,
      `HTTP error! status: ${response.status}`
    );
  }

  const result: ApiResponse = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to submit contact form');
  }

  return result;
}

// Services API
export async function getServicesData(
  category?: string
): Promise<ServicesPageData> {
  const endpoint = category
    ? `/services?category=${encodeURIComponent(category)}`
    : '/services';
  return fetchApi<ServicesPageData>(endpoint);
}

export async function getService(id: string): Promise<Service> {
  return fetchApi<Service>(`/services/${id}`);
}

export async function getServiceCategories() {
  return fetchApi('/services/meta/categories');
}

// Blog API
export async function getBlogsData(params?: {
  category?: string;
  tag?: string;
  limit?: number;
  offset?: number;
}): Promise<BlogsPageData> {
  const searchParams = new URLSearchParams();

  if (params?.category) searchParams.append('category', params.category);
  if (params?.tag) searchParams.append('tag', params.tag);
  if (params?.limit) searchParams.append('limit', params.limit.toString());
  if (params?.offset) searchParams.append('offset', params.offset.toString());

  const endpoint = `/blogs${
    searchParams.toString() ? `?${searchParams.toString()}` : ''
  }`;
  return fetchApi<BlogsPageData>(endpoint);
}

export async function getBlogPost(slug: string): Promise<BlogPost> {
  return fetchApi<BlogPost>(`/blogs/${slug}`);
}

export async function getBlogCategories() {
  return fetchApi('/blogs/meta/categories');
}

export async function getBlogTags() {
  return fetchApi('/blogs/meta/tags');
}
