// Shared types for the monorepo

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface SEOData {
  title: string;
  description: string;
  keywords?: string[];
  ogImage?: string;
  ogUrl?: string;
}

export interface HeroSection {
  title: string;
  subtitle: string;
  description: string;
  ctaText: string;
  ctaLink: string;
  backgroundImage?: string;
}

export interface BannerData {
  text: string;
  link?: string;
  isActive: boolean;
}

export interface HomePageData {
  seo: SEOData;
  banner: BannerData;
  hero: HeroSection;
  features: FeatureItem[];
  statistics: Statistics[];
  whyUs: WhyUs;
}

export interface FeatureItem {
  id: string;
  title: string;
  description: string;
  icon: string;
  link?: string;
  points: string[];
}

export interface Values {
  title: string;
  description: string;
  points: {
    title: string;
    subtitle: string;
    icon: string;
  }[];
}
export interface AboutPageData {
  seo: SEOData;
  title: string;
  description: string;
  mission: string;
  vision: string;
  values: Values;
  team: TeamMember[];
  story: string;
}

export interface TeamMember {
  id: string;
  name: string;
  position: string;
  bio: string;
  image?: string;
  social?: {
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
}

export interface ContactInfo {
  seo: SEOData;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    building: string;
  };
  socialMedia: {
    facebook?: string;
    twitter?: string;
    linkedin?: string;
    instagram?: string;
  };
}

export interface Service {
  id: string;
  title: string;
  description: string;
  features: string[];
  price?: string;
  image?: string;
  category: string;
}

export interface ServicesPageData {
  seo: SEOData;
  title: string;
  description: string;
  services: Service[];
}

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  publishedAt: string;
  updatedAt?: string;
  tags: string[];
  category: string;
  image?: string;
  slug: string;
}

export interface BlogsPageData {
  seo: SEOData;
  title: string;
  description: string;
  posts: BlogPost[];
  categories: string[];
  tags: string[];
}

export interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
}

export interface SiteConfig {
  name: string;
  description: string;
  url: string;
  logo?: string;
  navigation: NavigationItem[];
  footer: {
    copyright: string;
    links: NavigationItem[];
  };
}
export interface Statistics {
  id: string;
  title: string;
  value: string;
  color: string;
}

export interface WhyUs {
  title: string;
  description: string;
  points: {
    title: string;
    subtitle: string;
  }[];
  highLights: {
    title: string;
    buttonLabel: string;
    description: string;
    footer: string[];
  };
}

// Email-related types
export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface EmailConfig {
  fromEmail: string;
  fromName: string;
  replyToEmail?: string;
  adminEmail: string;
}

export interface SendEmailRequest {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
  replyTo?: string;
}

export interface EmailServiceResponse {
  success: boolean;
  messageId?: string | undefined;
  error?: string;
}
